<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon;

use App\Http\Controllers\Controller;
use App\Http\Resources\Marketplaces\Ozon\Products\MatchedProductCollection;
use App\Http\Resources\Marketplaces\Ozon\Products\ProductToMatchCollection;
use App\Http\Resources\Marketplaces\Ozon\Products\SuggestedMatchCollection;
use App\Modules\Marketplaces\Http\Requests\Ozon\Products\FindSuggestedMatchesRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Products\ManualMatchRequest;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Modules\Marketplaces\Services\Ozon\Actions\Products\FindSuggestedMatchesAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Products\GetMatchedProductsAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Products\GetProductsToMatchAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Products\ManualMatchAction;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ProductsController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly OzonPolicy $policy,
        private readonly GetMatchedProductsAction $getMatchedProductsAction,
        private readonly GetProductsToMatchAction $getProductsToMatchAction,
        private readonly FindSuggestedMatchesAction $findSuggestedMatchesAction,
        private readonly ManualMatchAction $manualMatchAction,
    ) {}

    /**
     * Получение списка сопоставленных товаров Ozon
     */
    public function getMatchedProducts(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $cabinetId = $this->getCabinetIdFromIntegration($integrationId);
            $matchedProducts = $this->getMatchedProductsAction->run($cabinetId, $integrationId);

            return $this->successResponse($matchedProducts);
        });
    }

    /**
     * Получение списка товаров к сопоставлению Ozon
     */
    public function getProductsToMatch(string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $cabinetId = $this->getCabinetIdFromIntegration($integrationId);
            $productsToMatch = $this->getProductsToMatchAction->run($cabinetId, $integrationId);

            return $this->successResponse($productsToMatch);
        });
    }

    /**
     * Поиск предлагаемых соответствий для товаров Ozon
     */
    public function findSuggestedMatches(FindSuggestedMatchesRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $cabinetId = $this->getCabinetIdFromIntegration($integrationId);
            $matchType = $request->validated('match_type');

            $suggestedMatches = $this->findSuggestedMatchesAction->run($cabinetId, $integrationId, $matchType);

            return $this->successResponse($suggestedMatches);
        });
    }

    /**
     * Ручное сопоставление товара Ozon
     */
    public function manualMatch(ManualMatchRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $cabinetId = $this->getCabinetIdFromIntegration($integrationId);
            $productToMatchId = $request->validated('product_to_match_id');
            $productId = $request->validated('product_id');

            $this->manualMatchAction->run($cabinetId, $productToMatchId, $productId);

            return $this->successResponse(['message' => 'Product matched successfully']);
        });
    }

    /**
     * Получение cabinet_id из интеграции Ozon
     */
    private function getCabinetIdFromIntegration(string $integrationId): string
    {
        $integration = DB::table('ozon_integrations')
            ->where('id', $integrationId)
            ->select('cabinet_id')
            ->first();

        if (!$integration) {
            throw new \RuntimeException('Integration not found');
        }

        return $integration->cabinet_id;
    }
}
