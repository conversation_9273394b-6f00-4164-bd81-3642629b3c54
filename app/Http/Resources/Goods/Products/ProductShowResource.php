<?php

namespace App\Http\Resources\Goods\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор товара */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $title Название товара */
            'title' => (string) $this->title,
            /** @var string $short_title Короткое название */
            'short_title' => (string) $this->short_title,
            /** @var int $type Тип товара */
            'type' => (int) $this->type,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $short_description Короткое описание */
            'short_description' => $this->short_description,
            /** @var bool $discounts_retail_sales Запретить скидки при продаже в розницу */
            'discounts_retail_sales' => (bool) $this->discounts_retail_sales,
            /** @var string|null $product_group_id Идентификатор группы товаров */
            'product_group_id' => $this->product_group_id,
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->country_id,
            /** @var string|null $article Артикул */
            'article' => $this->article,
            /** @var string|null $code Код товара */
            'code' => $this->code,
            /** @var int|null $inner_code Внутренний код */
            'inner_code' => $this->inner_code ? (int) $this->inner_code : null,
            /** @var string|null $external_code Внешний код */
            'external_code' => $this->external_code,
            /** @var string|null $measurement_unit_id Идентификатор единицы измерения */
            'measurement_unit_id' => $this->measurement_unit_id,
            /** @var string|null $brand_id Идентификатор бренда */
            'brand_id' => $this->brand_id,
            /** @var string|null $min_price Минимальная цена */
            'min_price' => $this->min_price ? (string) $this->min_price : null,
            /** @var string|null $min_price_currency_id Валюта минимальной цены */
            'min_price_currency_id' => $this->min_price_currency_id,
            /** @var string|null $purchase_price Закупочная цена */
            'purchase_price' => $this->purchase_price ? (string) $this->purchase_price : null,
            /** @var string|null $purchase_price_currency_id Валюта закупочной цены */
            'purchase_price_currency_id' => $this->purchase_price_currency_id,
            /** @var float|null $length Длина */
            'length' => $this->length ? (float) $this->length : null,
            /** @var float|null $width Ширина */
            'width' => $this->width ? (float) $this->width : null,
            /** @var float|null $height Высота */
            'height' => $this->height ? (float) $this->height : null,
            /** @var float|null $weight Вес */
            'weight' => $this->weight ? (float) $this->weight : null,
            /** @var float|null $volume Объем */
            'volume' => $this->volume ? (float) $this->volume : null,
            /** @var string|null $tax_id Идентификатор налога */
            'tax_id' => $this->tax_id,
            /** @var int|null $tax_system Система налогообложения */
            'tax_system' => $this->tax_system ? (int) $this->tax_system : null,
            /** @var string|null $indication_subject_calculation Признак предмета расчета */
            'indication_subject_calculation' => $this->indication_subject_calculation,
            /** @var string|null $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $category_id Идентификатор категории */
            'category_id' => $this->category_id,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_default Товар по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var array $barcodes Штрихкоды */
            'barcodes' => $this->barcodes ? (array) $this->barcodes : [],
            /** @var object $categories Категория */
            'categories' => $this->categories ?: (object) [],
            /** @var object $measurement_units Единица измерения */
            'measurement_units' => $this->measurement_units ?: (object) [],
            /** @var object $brands Бренд */
            'brands' => $this->brands ?: (object) [],
            /** @var object $contractors Контрагент */
            'contractors' => $this->contractors ?: (object) [],
            /** @var object $countries Страна */
            'countries' => $this->countries ?: (object) [],
            /** @var object $product_groups Группа товаров */
            'product_groups' => $this->product_groups ?: (object) [],
            /** @var string|null $image URL изображения */
            'image' => $this->image,
            /** @var object $features Характеристики товара */
            'features' => $this->features ?: (object) [],
        ];
    }
}
